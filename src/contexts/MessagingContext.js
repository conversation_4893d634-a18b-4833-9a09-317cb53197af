/**
 * Messaging Context
 * Manages global messaging state including unread counts and real-time updates
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getUnreadConversationsCount } from '../services/messagingService';

const MessagingContext = createContext();

export const useMessaging = () => {
  const context = useContext(MessagingContext);
  if (!context) {
    throw new Error('useMessaging must be used within a MessagingProvider');
  }
  return context;
};

export const MessagingProvider = ({ children }) => {
  const [unreadConversations, setUnreadConversations] = useState(0);
  const [totalUnreadMessages, setTotalUnreadMessages] = useState(0);
  const [isPolling, setIsPolling] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);

  // Get auth code from storage
  const getAuthCode = useCallback(async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const user = JSON.parse(userData);
        return user.authCode || user.auth_code;
      }
      return null;
    } catch (error) {
      console.error('Error getting auth code:', error);
      return null;
    }
  }, []);

  // Update unread counts
  const updateUnreadCounts = useCallback(async () => {
    try {
      const authCode = await getAuthCode();
      if (!authCode) {
        console.warn('No auth code found, skipping unread count update');
        return;
      }

      console.log('📊 MESSAGING: Updating unread counts...');
      const response = await getUnreadConversationsCount(authCode);
      
      if (response.success && response.data) {
        const { unread_conversations, total_unread_messages } = response.data;
        
        setUnreadConversations(unread_conversations || 0);
        setTotalUnreadMessages(total_unread_messages || 0);
        setLastUpdateTime(new Date().toISOString());
        
        console.log(`📊 MESSAGING: Updated counts - Conversations: ${unread_conversations}, Messages: ${total_unread_messages}`);
      } else {
        console.warn('Failed to get unread counts:', response.error);
      }
    } catch (error) {
      console.error('Error updating unread counts:', error);
    }
  }, [getAuthCode]);

  // Start polling for unread count updates
  const startPolling = useCallback(() => {
    if (isPolling) return;
    
    console.log('🔄 MESSAGING: Starting unread count polling');
    setIsPolling(true);
    
    // Initial update
    updateUnreadCounts();
    
    // Poll every 30 seconds
    const interval = setInterval(() => {
      updateUnreadCounts();
    }, 30000);
    
    return () => {
      console.log('⏹️ MESSAGING: Stopping unread count polling');
      clearInterval(interval);
      setIsPolling(false);
    };
  }, [isPolling, updateUnreadCounts]);

  // Stop polling
  const stopPolling = useCallback(() => {
    setIsPolling(false);
  }, []);

  // Mark conversation as read locally (optimistic update)
  const markConversationAsReadLocally = useCallback((conversationUuid) => {
    console.log(`📖 MESSAGING: Marking conversation ${conversationUuid} as read locally`);
    setUnreadConversations(prev => Math.max(0, prev - 1));
    // Note: We don't update totalUnreadMessages here as we don't know how many messages were unread
    // The next polling cycle will get the accurate count
  }, []);

  // Force refresh unread counts
  const refreshUnreadCounts = useCallback(() => {
    console.log('🔄 MESSAGING: Force refreshing unread counts');
    updateUnreadCounts();
  }, [updateUnreadCounts]);

  // Initialize polling when component mounts
  useEffect(() => {
    const cleanup = startPolling();
    return cleanup;
  }, [startPolling]);

  const value = {
    // State
    unreadConversations,
    totalUnreadMessages,
    isPolling,
    lastUpdateTime,
    
    // Actions
    updateUnreadCounts,
    startPolling,
    stopPolling,
    markConversationAsReadLocally,
    refreshUnreadCounts,
  };

  return (
    <MessagingContext.Provider value={value}>
      {children}
    </MessagingContext.Provider>
  );
};

export default MessagingContext;
