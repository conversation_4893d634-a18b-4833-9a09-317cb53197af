{"expo": {"name": "BFI Education SIS", "slug": "edu-sis", "version": "1.0.0", "orientation": "default", "icon": "./assets/app_logo.png", "userInterfaceStyle": "light", "newArchEnabled": true, "plugins": [["@react-native-firebase/app", {"ios": {"googleServicesFile": "./GoogleService-Info.plist"}, "android": {"googleServicesFile": "./google-services.json"}}], "@react-native-firebase/messaging", ["expo-notifications", {"color": "#007AFF"}], ["expo-build-properties", {"ios": {"useFrameworks": "static"}, "android": {"compileSdkVersion": 35, "targetSdkVersion": 35, "minSdkVersion": 24, "buildToolsVersion": "35.0.0"}}]], "splash": {"image": "./assets/app_logo.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.edunovaasia.edusis", "buildNumber": "30", "googleServicesFile": "./GoogleService-Info.plist", "config": {"usesNonExemptEncryption": false}, "infoPlist": {"UIBackgroundModes": ["remote-notification"], "UISupportedInterfaceOrientations": ["UIInterfaceOrientationPortrait"], "UISupportedInterfaceOrientations~ipad": ["UIInterfaceOrientationPortrait", "UIInterfaceOrientationPortraitUpsideDown", "UIInterfaceOrientationLandscapeLeft", "UIInterfaceOrientationLandscapeRight"], "NSUserTrackingUsageDescription": "This app does not track users across apps or websites.", "ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/app_logo.png", "backgroundColor": "#ffffff"}, "package": "com.edunovaasia.edusis", "googleServicesFile": "./google-services.json", "screenOrientation": "portrait", "compileSdkVersion": 35, "targetSdkVersion": 35, "minSdkVersion": 24, "versionCode": 31}, "web": {"favicon": "./assets/app_logo.png"}, "extra": {"eas": {"projectId": "5c37501a-d3f1-49d2-bf38-28446fc1b0bb"}}, "runtimeVersion": "1.0.0", "updates": {"url": "https://u.expo.dev/5c37501a-d3f1-49d2-bf38-28446fc1b0bb"}}}