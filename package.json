{"name": "edu-sis", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:dev": "eas build --profile development", "build:dev:android": "eas build --profile development --platform android", "build:dev:ios": "eas build --profile development --platform ios", "build:preview": "eas build --profile preview", "build:preview:android": "eas build --profile preview --platform android", "build:preview:ios": "eas build --profile preview --platform ios", "build:prod": "eas build --profile production", "build:prod:android": "eas build --profile production --platform android", "build:prod:ios": "eas build --profile production --platform ios", "build:all": "eas build --profile production --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "submit:all": "eas submit --platform all", "update": "eas update", "update:preview": "eas update --branch preview", "update:production": "eas update --branch production", "prebuild": "expo prebuild", "prebuild:clean": "expo prebuild --clean", "clean": "expo install --fix && npm run prebuild:clean", "eas:build": "./scripts/eas-automation.sh build", "eas:submit": "./scripts/eas-automation.sh submit", "eas:update": "./scripts/eas-automation.sh update", "eas:deploy": "./scripts/eas-automation.sh deploy", "eas:status": "./scripts/eas-automation.sh status", "eas:workflow": "./scripts/eas-automation.sh workflow", "workflow:build-ios": "eas workflow:run build-ios-production.yml", "workflow:build-android": "eas workflow:run build-android-production.yml", "workflow:build-all": "eas workflow:run build-all-platforms.yml", "workflow:preview": "eas workflow:run preview-deployment.yml", "workflow:production": "eas workflow:run production-deployment.yml", "firebase:verify": "node scripts/verify-firebase-config.js", "firebase:update-config": "./scripts/update-firebase-config.sh", "cleanup": "./scripts/cleanup-project.sh"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-native-fontawesome": "^0.3.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-firebase/app": "^21.13.0", "@react-native-firebase/messaging": "^21.13.0", "@react-native-picker/picker": "^2.8.1", "@react-navigation/elements": "^2.3.1", "@react-navigation/native": "^7.0.19", "@react-navigation/native-stack": "^7.3.3", "expo": "~52.0.46", "expo-build-properties": "~0.13.3", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-document-picker": "~13.0.3", "expo-image-picker": "~16.0.3", "expo-modules-core": "~2.2.3", "expo-notifications": "~0.29.14", "expo-screen-orientation": "~8.0.4", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-updates": "~0.27.4", "react": "18.3.1", "react-native": "0.76.9", "react-native-get-random-values": "^1.11.0", "react-native-modal-datetime-picker": "^18.0.0", "react-native-orientation-locker": "^1.7.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "^18.0.0"}, "private": true}